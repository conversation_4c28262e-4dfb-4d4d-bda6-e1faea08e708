import React, { useState } from 'react';
import CustomModal from '../../CustomModal';
import { FiSearch, FiArrowLeft, FiArrowRight } from 'react-icons/fi';


interface HoldOrdersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Order {
  orderNo: string;
  tableNo: string;
  operatorName: string;
  customer: string;
  amount: number;
  recordDate: string;
}

const HoldOrdersModal: React.FC<HoldOrdersModalProps> = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Mock data for demonstration
  const orders: Order[] = [
    // This would be populated from an API in a real implementation
  ];

  // Filter orders based on search term
  const filteredOrders = orders.filter(order =>
    order.orderNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.operatorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.customer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSearch = (): void => {
    console.log('Searching for:', searchTerm);
    // Implement search functionality here
  };

  const handlePrevPage = (): void => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = (): void => {
    // Assuming we have a total number of pages
    const totalPages = Math.ceil(filteredOrders.length / 10) || 1;
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleLoadOrder = (): void => {
    console.log('Loading selected order');
    onClose();
  };

  const footer = (
    <div className="flex justify-end gap-4">
      <button
        onClick={onClose}
        className="px-8 py-3 border border-orange-500 text-orange-500 font-medium rounded-full hover:bg-orange-50 transition-colors"
      >
        Cancel
      </button>
      <button
        onClick={handleLoadOrder}
        className="px-8 py-3 bg-orange-500 text-white font-medium rounded-full hover:bg-orange-600 transition-colors"
      >
        Load
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Hold Orders"
      width="max-w-6xl"
      footer={footer}
      zIndex={1000000} // Extremely high z-index to appear above other modals
    >
      <div className="p-6">
        {/* Search Bar */}
        <div className="mb-6 flex items-center border border-gray-200 rounded-lg overflow-hidden">
          <div className="pl-4">
            <FiSearch className="text-gray-400" size={20} />
          </div>
          <input
            type="text"
            placeholder="Search Order"
            className="w-full p-3 focus:outline-none text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button
            onClick={handleSearch}
            className="px-6 py-3 bg-gray-100 border-l border-gray-300 hover:bg-gray-200 transition-colors"
          >
            →
          </button>
        </div>

        {/* Table Headers */}
        <div className="grid grid-cols-6 gap-4 py-3 border-b border-gray-200 font-medium text-gray-600">
          <div>Order No</div>
          <div>Table No</div>
          <div>Operator Name</div>
          <div>Customer</div>
          <div>Amount</div>
          <div>Record Date</div>
        </div>

        {/* Table Content */}
        <div className="overflow-y-auto min-h-[300px] max-h-[50vh]">
          {filteredOrders.length > 0 ? (
            filteredOrders.map((order, index) => (
              <div
                key={index}
                className="grid grid-cols-6 gap-4 py-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer"
              >
                <div>{order.orderNo}</div>
                <div>{order.tableNo}</div>
                <div>{order.operatorName}</div>
                <div>{order.customer}</div>
                <div>${order.amount.toFixed(2)}</div>
                <div>{order.recordDate}</div>
              </div>
            ))
          ) : (
            <div className="py-8 text-center text-gray-500">
              No orders found
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={handlePrevPage}
              className="px-4 py-2 border border-gray-300 rounded-l-lg text-gray-600 hover:bg-gray-100 flex items-center transition-colors"
              disabled={currentPage === 1}
            >
              <FiArrowLeft className="mr-1" /> Previous
            </button>
            <div className="px-4 py-2 border-t border-b border-gray-300 bg-white">
              {currentPage} of {Math.ceil(filteredOrders.length / 10) || 1}
            </div>
            <button
              onClick={handleNextPage}
              className="px-4 py-2 border border-gray-300 rounded-r-lg text-gray-600 hover:bg-gray-100 flex items-center transition-colors"
              disabled={currentPage >= (Math.ceil(filteredOrders.length / 10) || 1)}
            >
              Next <FiArrowRight className="ml-1" />
            </button>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default HoldOrdersModal;