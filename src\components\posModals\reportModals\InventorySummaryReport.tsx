import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch, FiPrinter } from "react-icons/fi";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";

interface InventorySummaryReportProps {
  isOpen: boolean;
  onClose: () => void;
}

interface InventoryItem {
  id: string;
  ingredientName: string;
  unitOfMeasurement: string;
  stockRemaining: string;
}

const InventorySummaryReport: React.FC<InventorySummaryReportProps> = ({
  isOpen,
  onClose,
}) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(),
    new Date(),
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Sample data - replace with actual data from your backend
  const inventoryItems: InventoryItem[] = [
    {
      id: "PR-001",
      ingredientName: "Red Wine",
      unitOfMeasurement: "ml",
      stockRemaining: "50%",
    },
    {
      id: "PR-002",
      ingredientName: "Pepsi cola",
      unitOfMeasurement: "ml",
      stockRemaining: "50%",
    },
    {
      id: "PR-001",
      ingredientName: "Red Wine",
      unitOfMeasurement: "ml",
      stockRemaining: "50%",
    },
    {
      id: "PR-002",
      ingredientName: "Pepsi cola",
      unitOfMeasurement: "ml",
      stockRemaining: "50%",
    },
    {
      id: "PR-001",
      ingredientName: "Red Wine",
      unitOfMeasurement: "ml",
      stockRemaining: "50%",
    },
  ];

  const handlePrint = () => {
    window.print();
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
          onClick={() => setCurrentPage((prev) => prev + 1)}
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4 font-bold">
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handlePrint}
          className="px-14 py-2 bg-black text-white rounded-full cursor-pointer transition-colors flex items-center gap-2"
        >
          Print <FiPrinter size={20} />
        </button>
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Real-Time Inventory Summary Report"
      width="max-w-6xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Inventory Item"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md  cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  // showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-[#9C9C9C] text-xs font-extralight border-b border-[#E4E4E4]">
                <th className="pb-3">ID</th>
                <th className="pb-3">Ingredient Name</th>
                <th className="pb-3">Unit of Measurement</th>
                <th className="pb-3">Stock Remaining</th>
              </tr>
            </thead>
            <tbody>
              {inventoryItems.map((item, index) => (
                <tr key={index} className="border-b text-sm border-[#E4E4E4]">
                  <td className="py-4">{item.id}</td>
                  <td className="py-4">{item.ingredientName}</td>
                  <td className="py-4">{item.unitOfMeasurement}</td>
                  <td className="py-4">{item.stockRemaining}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </CustomModal>
  );
};

export default InventorySummaryReport;
