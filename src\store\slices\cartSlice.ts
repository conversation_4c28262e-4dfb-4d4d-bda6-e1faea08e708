import { createSlice } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../store';
import type { ModifierOption } from '../../types/modifiers';

// Generate random invoice number: # + 14 random alphanumeric characters
const generateInvoiceNumber = (): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '#';
  for (let i = 0; i < 14; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  discountType?: string;
  discountAmount?: string;
  note?: string;
  originalPrice?: number;
  modifiers?: ModifierOption[];
  selectedModifiers?: ModifierOption[];
  hasModifiers?: boolean;
  selectedForRefund?: boolean;
}

interface SelectedCoupon {
  _id: string;
  series: string;
  description: string;
  discount: number;
  discountType: string;
}

interface CartState {
  items: CartItem[];
  isLoading: boolean;
  error: string | null;
  discountAmount: string;
  discountReason: string;
  taxPercentage: number;
  tipAmount: string;
  loyaltyPercentage: number;
  loyaltyFixedAmount: string;
  loyaltyType: string; // 'percentage' or 'fixed'
  invoiceNumber: string;
  selectAllForRefund: boolean;
  isRefundMode: boolean;
  selectedCoupon: SelectedCoupon | null;
  couponOfferAmount: number;
}

const initialState: CartState = {
  items: [],
  isLoading: false,
  error: null,
  discountAmount: '0',
  discountReason: '',
  taxPercentage: 0,
  tipAmount: '0',
  loyaltyPercentage: 0,
  loyaltyFixedAmount: '0',
  loyaltyType: '',
  invoiceNumber: generateInvoiceNumber(),
  selectAllForRefund: false,
  isRefundMode: false,
  selectedCoupon: null,
  couponOfferAmount: 0,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addItem: (state, action: PayloadAction<CartItem>) => {
      const existingItem = state.items.find(item => item.id === action.payload.id);
      if (existingItem) {
        // If item already exists, increase quantity
        existingItem.quantity += 1;
      } else {
        // Otherwise add new item with proper modifier handling
        const newItem = {
          ...action.payload,
          // Ensure selectedModifiers is set if modifiers exist
          selectedModifiers: action.payload.selectedModifiers || action.payload.modifiers || []
        };
        state.items.push(newItem);
      }
    },
    removeItem: (state, action: PayloadAction<string>) => {
      // Remove item by id
      state.items = state.items.filter(item => item.id !== action.payload);
    },
    increaseQuantity: (state, action: PayloadAction<string>) => {
      const item = state.items.find(item => item.id === action.payload);
      if (item) {
        item.quantity += 1;
      }
    },
    decreaseQuantity: (state, action: PayloadAction<string>) => {
      const item = state.items.find(item => item.id === action.payload);
      if (item && item.quantity > 1) {
        item.quantity -= 1;
      } else if (item && item.quantity === 1) {
        // If quantity becomes 0, remove the item
        state.items = state.items.filter(item => item.id !== action.payload);
      }
    },
    clearCart: (state) => {
      state.items = [];
      state.isRefundMode = false;
      state.selectAllForRefund = false;
    },
    setCartItems: (state, action: PayloadAction<CartItem[]>) => {
      state.items = action.payload;
    },
    updateItemPrice: (state, action: PayloadAction<{ id: string, price: number }>) => {
      const item = state.items.find(item => item.id === action.payload.id);
      if (item) {
        item.price = action.payload.price;
      }
    },
    applyItemDiscount: (state, action: PayloadAction<{
      id: string,
      newPrice: number,
      discountType: string,
      discountAmount: string,
      note: string,
      modifiers?: ModifierOption[],
      selectedModifiers?: ModifierOption[]
    }>) => {
      const item = state.items.find(item => item.id === action.payload.id);
      if (item) {
        // Store the original price if not already stored
        if (!item.originalPrice) {
          item.originalPrice = item.price;
        }

        // Update the item with discount information
        item.price = action.payload.newPrice;
        item.discountType = action.payload.discountType;
        item.discountAmount = action.payload.discountAmount;
        item.note = action.payload.note;

        // Update modifiers if provided
        if (action.payload.modifiers) {
          item.modifiers = action.payload.modifiers;
          item.hasModifiers = action.payload.modifiers.length > 0;
        }

        // Update selectedModifiers if provided
        if (action.payload.selectedModifiers) {
          item.selectedModifiers = action.payload.selectedModifiers;
        }
      }
    },
    updateItemModifiers: (state, action: PayloadAction<{
      id: string,
      selectedModifiers: ModifierOption[]
    }>) => {
      const item = state.items.find(item => item.id === action.payload.id);
      if (item) {
        item.selectedModifiers = action.payload.selectedModifiers;
        item.modifiers = action.payload.selectedModifiers;
        item.hasModifiers = action.payload.selectedModifiers.length > 0;
      }
    },
    setDiscountAmount: (state, action: PayloadAction<string>) => {
      state.discountAmount = action.payload;
    },
    setDiscountReason: (state, action: PayloadAction<string>) => {
      state.discountReason = action.payload;
    },
    setTaxPercentage: (state, action: PayloadAction<number>) => {
      state.taxPercentage = action.payload;
    },
    setTipAmount: (state, action: PayloadAction<string>) => {
      state.tipAmount = action.payload;
    },
    setLoyaltyPercentage: (state, action: PayloadAction<number>) => {
      state.loyaltyPercentage = action.payload;
    },
    setLoyaltyFixedAmount: (state, action: PayloadAction<string>) => {
      state.loyaltyFixedAmount = action.payload;
    },
    setLoyaltyType: (state, action: PayloadAction<string>) => {
      state.loyaltyType = action.payload;
    },
    setSelectedCoupon: (state, action: PayloadAction<SelectedCoupon | null>) => {
      state.selectedCoupon = action.payload;
    },
    setCouponOfferAmount: (state, action: PayloadAction<number>) => {
      state.couponOfferAmount = action.payload;
    },
    clearCoupon: (state) => {
      state.selectedCoupon = null;
      state.couponOfferAmount = 0;
    },
    generateNewInvoiceNumber: (state) => {
      state.invoiceNumber = generateInvoiceNumber();
    },
    loadOrderData: (state, action: PayloadAction<{
      items: any[];
      productWithQty?: any[];
      selectedModifiers?: any[];
      discountAmount?: string;
      taxPercentage?: number;
      invoiceNumber?: string;
    }>) => {
      // Clear existing cart
      state.items = [];

      // Load items from order
      const loadedItems: CartItem[] = action.payload.items.map((item, index) => {
        // Find corresponding productWithQty data for discount information
        const productQtyData = action.payload.productWithQty?.find(
          (pqty: any) => pqty.productId === (item.ProductId || item._id)
        );

        // Calculate discount information
        let discountType = '';
        let discountAmount = '';
        let finalPrice = item.price;
        let originalPrice = item.price;

        if (productQtyData && productQtyData.discount > 0) {
          discountAmount = productQtyData.discount.toString();
          // Determine discount type based on the discount value and prices
          if (productQtyData.oldAmount && productQtyData.newAmount) {
            const discountValue = productQtyData.oldAmount - productQtyData.newAmount;
            const discountPercentage = (discountValue / productQtyData.oldAmount) * 100;

            // If discount equals the percentage calculation, it's likely a percentage discount
            if (Math.abs(discountPercentage - productQtyData.discount) < 1) {
              discountType = 'percentage';
            } else {
              discountType = 'fixed';
            }
          } else {
            // Default to fixed if we can't determine
            discountType = 'fixed';
          }

          finalPrice = productQtyData.newAmount / productQtyData.qty;
          originalPrice = productQtyData.oldAmount / productQtyData.qty;
        }

        // Handle modifiers - check both product-level and root-level selectedModifiers
        let itemModifiers: any[] = [];
        let itemSelectedModifiers: any[] = [];

        // First, try to get modifiers from the product itself
        if (item.modifiers?.data && Array.isArray(item.modifiers.data)) {
          itemModifiers = item.modifiers.data;
          itemSelectedModifiers = item.modifiers.data;
        }

        // If no modifiers in product, check if there are selectedModifiers at root level for this product
        if (itemModifiers.length === 0 && action.payload.selectedModifiers && Array.isArray(action.payload.selectedModifiers)) {
          // Filter selectedModifiers that might belong to this product
          // This is a fallback in case modifiers are stored at root level
          itemSelectedModifiers = action.payload.selectedModifiers;
          itemModifiers = action.payload.selectedModifiers;
        }

        console.log(`Loading modifiers for item ${item.name}:`, {
          productModifiers: item.modifiers,
          rootSelectedModifiers: action.payload.selectedModifiers,
          finalModifiers: itemModifiers
        });

        return {
          id: item.ProductId || item._id || `loaded-item-${index}`,
          name: item.name,
          price: finalPrice,
          quantity: productQtyData?.qty || item.quantity || 1,
          image: item.Product_pic || '/default-product-image.jpg',
          modifiers: itemModifiers,
          selectedModifiers: itemSelectedModifiers,
          hasModifiers: Boolean(itemModifiers.length),
          selectedForRefund: false,
          discountType: discountType || undefined,
          discountAmount: discountAmount || undefined,
          originalPrice: originalPrice !== finalPrice ? originalPrice : undefined,
          note: productQtyData?.reason || ''
        };
      });

      state.items = loadedItems;

      // Set refund mode to true when loading order data
      state.isRefundMode = true;

      // Load other order data if provided
      if (action.payload.discountAmount) {
        state.discountAmount = action.payload.discountAmount;
      }
      if (action.payload.taxPercentage) {
        state.taxPercentage = action.payload.taxPercentage;
      }
      if (action.payload.invoiceNumber) {
        state.invoiceNumber = action.payload.invoiceNumber;
      }
    },
    toggleSelectAllForRefund: (state) => {
      state.selectAllForRefund = !state.selectAllForRefund;
      // Update all items to match the select all state
      state.items.forEach(item => {
        item.selectedForRefund = state.selectAllForRefund;
      });
    },
    toggleItemRefundSelection: (state, action: PayloadAction<string>) => {
      const item = state.items.find(item => item.id === action.payload);
      if (item) {
        item.selectedForRefund = !item.selectedForRefund;

        // Update selectAllForRefund based on whether all items are selected
        state.selectAllForRefund = state.items.every(item => item.selectedForRefund);
      }
    },
    exitRefundMode: (state) => {
      state.isRefundMode = false;
      state.selectAllForRefund = false;
      // Reset all item refund selections
      state.items.forEach(item => {
        item.selectedForRefund = false;
      });
    },
  },
});

export const {
  addItem,
  removeItem,
  increaseQuantity,
  decreaseQuantity,
  clearCart,
  setCartItems,
  updateItemPrice,
  applyItemDiscount,
  updateItemModifiers,
  setDiscountAmount,
  setDiscountReason,
  setTaxPercentage,
  setTipAmount,
  setLoyaltyPercentage,
  setLoyaltyFixedAmount,
  setLoyaltyType,
  setSelectedCoupon,
  setCouponOfferAmount,
  clearCoupon,
  generateNewInvoiceNumber,
  loadOrderData,
  toggleSelectAllForRefund,
  toggleItemRefundSelection,
  exitRefundMode
} = cartSlice.actions;

// Selectors
export const selectCartItems = (state: RootState) => state.cart.items;
export const selectCartTotal = (state: RootState) =>
  state.cart.items.reduce((total, item) => total + (item.price * item.quantity), 0);
export const selectCartItemsCount = (state: RootState) =>
  state.cart.items.reduce((count, item) => count + item.quantity, 0);

// New selectors
export const selectDiscountAmount = (state: RootState) => state.cart.discountAmount;
export const selectDiscountReason = (state: RootState) => state.cart.discountReason;
export const selectTaxPercentage = (state: RootState) => state.cart.taxPercentage;
export const selectTipAmount = (state: RootState) => state.cart.tipAmount;
export const selectLoyaltyPercentage = (state: RootState) => state.cart.loyaltyPercentage;
export const selectLoyaltyFixedAmount = (state: RootState) => state.cart.loyaltyFixedAmount;
export const selectLoyaltyType = (state: RootState) => state.cart.loyaltyType;
export const selectInvoiceNumber = (state: RootState) => state.cart.invoiceNumber;
export const selectAllForRefund = (state: RootState) => state.cart.selectAllForRefund;
export const selectIsRefundMode = (state: RootState) => state.cart.isRefundMode;
export const selectSelectedCoupon = (state: RootState) => state.cart.selectedCoupon;
export const selectCouponOfferAmount = (state: RootState) => state.cart.couponOfferAmount;

// Dynamic loyalty amount selector that calculates based on type
export const selectLoyaltyAmount = (state: RootState) => {
  const cartTotal = selectCartTotal(state);
  const loyaltyType = state.cart.loyaltyType;
  const loyaltyPercentage = state.cart.loyaltyPercentage;
  const loyaltyFixedAmount = Number(state.cart.loyaltyFixedAmount) || 0;

  if (loyaltyType === 'percentage') {
    return (cartTotal * loyaltyPercentage) / 100;
  } else if (loyaltyType === 'fixed') {
    return loyaltyFixedAmount;
  }
  return 0;
};

// Calculate the final total amount
export const selectFinalTotal = (state: RootState) => {
  const cartTotal = selectCartTotal(state);
  const discountAmount = Number(state.cart.discountAmount) || 0;
  const taxPercentage = state.cart.taxPercentage;
  const tipAmount = Number(state.cart.tipAmount) || 0;
  const loyaltyAmount = selectLoyaltyAmount(state);

  return (
    cartTotal -
    discountAmount -
    loyaltyAmount +
    (cartTotal * (taxPercentage / 100)) +
    tipAmount
  );
};

export default cartSlice.reducer;
