import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format, isWithinInterval } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { useGetLastMonthOrdersQuery } from "../../../store/api/pos/customer";

interface AccountPaymentReportProps {
  isOpen: boolean;
  onClose: () => void;
}

interface PaymentRecord {
  customerName: string;
  date: string;
  paymentMethod: string;
  paymentAmount: string;
  rawDate: Date | undefined;
}

const AccountPaymentReport: React.FC<AccountPaymentReportProps> = ({
  isOpen,
  onClose,
}) => {
  // Initialize date range with null values to show all orders by default
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const userId = localStorage.getItem("userId")
  const { data, isLoading, error } = useGetLastMonthOrdersQuery(userId || " ")
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
      setIsCalendarOpen(false);
      setCurrentPage(1);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "All Orders";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Transform API data to PaymentRecord format
  const payments: PaymentRecord[] = data ? data.map((order: any) => {
    let rawDate: Date | undefined = undefined;
    try {
      if (order.createdAt) {
        rawDate = new Date(order.createdAt);
      }
    } catch (e) {
      console.error("Error parsing date:", e);
    }

    const customerName = order.customerId ?
      `${order.customerId.FirstName || ''} ${order.customerId.LastName || ''}`.trim() :
      'Guest';

    const paymentAmount = order.dueamount ?
      `$${parseFloat(order.dueamount).toFixed(2)}` :
      '$0.00';

    const paymentMethod = typeof order.paymentType === 'object' && order.paymentType?.name
      ? order.paymentType.name
      : order.paymentType || 'N/A';

    const date = rawDate ?
      format(rawDate, "yyyy-MM-dd") :
      'N/A';

    return {
      customerName,
      date,
      paymentMethod,
      paymentAmount,
      rawDate
    };
  }) : [];

  // Filter payments based on search term and date range
  const filteredPayments = payments.filter(payment => {
    // Filter by search term
    const matchesSearch = payment.customerName.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by date range only if both dates are selected
    const matchesDateRange = !startDate || !endDate || !payment.rawDate
      ? true
      : isWithinInterval(payment.rawDate, {
        start: startDate,
        end: endDate
      });

    return matchesSearch && matchesDateRange;
  });

  // Calculate total payment amount
  const calculateTotal = () => {
    return filteredPayments.reduce((total, payment) => {
      const amount = parseFloat(payment.paymentAmount.replace('$', '')) || 0;
      return total + amount;
    }, 0);
  };

  const total = `$${calculateTotal().toFixed(2)}`;

  // Calculate pagination
  const totalPages = Math.ceil(filteredPayments.length / itemsPerPage);
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages || totalPages === 0;

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPayments.slice(indexOfFirstItem, indexOfLastItem);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Reset to first page when date range changes
  useEffect(() => {
    setCurrentPage(1);
  }, [dateRange]);

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${isFirstPage ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={() => !isFirstPage && setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={isFirstPage}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${isLastPage ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={() => !isLastPage && setCurrentPage((prev) => prev + 1)}
          disabled={isLastPage}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Account Payment Report"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Payments Table */}
        <div className="mb-6">
          <div className="grid grid-cols-4 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg">
            <div className="text-gray-600 font-medium">Customer Name</div>
            <div className="text-gray-600 font-medium">Date</div>
            <div className="text-gray-600 font-medium">Payment Method</div>
            <div className="text-gray-600 font-medium">Payment Amount</div>
          </div>

          <div className="divide-y divide-gray-100">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
              </div>
            ) : error ? (
              <div className="text-center py-4 text-red-500">Error loading payments</div>
            ) : currentItems.length === 0 ? (
              <div className="text-center py-4">No payments found</div>
            ) : (
              currentItems.map((payment, index) => (
                <div
                  key={index}
                  className="grid grid-cols-4 gap-4 px-4 py-3 hover:bg-gray-50"
                >
                  <div>{payment.customerName}</div>
                  <div>{payment.date}</div>
                  <div>{payment.paymentMethod}</div>
                  <div>{payment.paymentAmount}</div>
                </div>
              ))
            )}
          </div>

          {!isLoading && !error && filteredPayments.length > 0 && (
            <div className="text-right text-sm text-gray-500 mt-2 pr-4">
              Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredPayments.length)} of {filteredPayments.length} payments
            </div>
          )}
        </div>

        <div className="text-right p-3 border-y border-[#E4E4E4] mr-2">
          <span className="font-medium mr-3 text-lg">Total:</span>
          <span className="text-orange font-bold text-2xl">{total}</span>
        </div>
      </div>
    </CustomModal>
  );
};

export default AccountPaymentReport;