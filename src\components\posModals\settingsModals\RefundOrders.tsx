import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format } from "date-fns";
import { FiSearch } from "react-icons/fi";
interface RefundOrdersProps {
  isOpen: boolean;
  onClose: () => void;
}

interface OrderRecord {
  receiptNumber: string;
  operatorName: string;
  customer: string;
  recordDate: string;
  tax: string;
  amount: string;
  receivedAmount: string;
  dueAmount: string;
  date: string;
}

const RefundableOrders: React.FC<RefundOrdersProps> = ({ isOpen, onClose }) => {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(),
    new Date(),
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Mock data
  const orders: OrderRecord[] = [
    {
      receiptNumber: "REC001",
      operatorName: "John Doe",
      customer: "Alice Smith",
      recordDate: "2024-08-23",
      tax: "$5.25",
      amount: "$100.75",
      receivedAmount: "$90.00",
      dueAmount: "$10.75",
      date: "2024-08-23",
    },
    {
      receiptNumber: "REC002",
      operatorName: "Jane Doe",
      customer: "Bob Johnson",
      recordDate: "2024-08-22",
      tax: "$6.50",
      amount: "$150.50",
      receivedAmount: "$140.00",
      dueAmount: "$10.50",
      date: "2024-08-22",
    },
    {
      receiptNumber: "REC001",
      operatorName: "John Doe",
      customer: "Alice Smith",
      recordDate: "2024-08-23",
      tax: "$5.25",
      amount: "$100.75",
      receivedAmount: "$90.00",
      dueAmount: "$10.75",
      date: "2024-08-23",
    },
    {
      receiptNumber: "REC002",
      operatorName: "Jane Doe",
      customer: "Bob Johnson",
      recordDate: "2024-08-22",
      tax: "$6.50",
      amount: "$150.50",
      receivedAmount: "$140.00",
      dueAmount: "$10.50",
      date: "2024-08-22",
    },
    {
      receiptNumber: "REC001",
      operatorName: "John Doe",
      customer: "Alice Smith",
      recordDate: "2024-08-23",
      tax: "$5.25",
      amount: "$100.75",
      receivedAmount: "$90.00",
      dueAmount: "$10.75",
      date: "2024-08-23",
    },
  ];

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className="px-4 py-2 text-sm font-medium text-[#9C9C9C] cursor-pointer"
          onClick={() => setCurrentPage((prev) => prev + 1)}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-12 py-2 border border-orange text-orange text-xl font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Refund Orders"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md  cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  // showSelectionPreview={true}
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Orders Table */}
        <div className="mb-6 overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left font-poppins font-extralight text-xs text-[#9C9C9C] border-b-1 border-[#E4E4E4]">
                <th className="px-4 py-2 ">Receipt Number</th>
                <th className="px-4 py-2 ">Operator Name</th>
                <th className="px-4 py-2 ">Customer</th>
                <th className="px-4 py-2 ">Record Date</th>
                <th className="px-4 py-2 ">Tax</th>
                <th className="px-4 py-2 ">Amount</th>
                <th className="px-4 py-2 ">Received Amount</th>
                <th className="px-4 py-2 ">Due Amount</th>
                <th className="px-4 py-2 ">Date</th>
                <th className="px-4 py-2 ">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y font-poppins divide-gray-100 ">
              {orders.map((order, index) => (
                <tr key={index} className=" border-b-1 border-[#E4E4E4]">
                  <td className="px-4 py-3">{order.receiptNumber}</td>
                  <td className="px-4 py-3">{order.operatorName}</td>
                  <td className="px-4 py-3">{order.customer}</td>
                  <td className="px-4 py-3">{order.recordDate}</td>
                  <td className="px-4 py-3">{order.tax}</td>
                  <td className="px-4 py-3">{order.amount}</td>
                  <td className="px-4 py-3">{order.receivedAmount}</td>
                  <td className="px-4 py-3">{order.dueAmount}</td>
                  <td className="px-4 py-3">{order.date}</td>
                  <td className="px-4 py-3">
                    <button className="bg-orange text-white px-8 py-1 rounded-full text-md font-medium cursor-pointer transition-colors">
                      Refund
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </CustomModal>
  );
};

export default RefundableOrders;
