// Print utilities for receipt printing

export interface PrintReceiptData {
  orderNo: string;
  table?: string;
  customer: string;
  date: string;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  tax: number;
  surcharge: number;
  total: number;
  payment: string;
  received: number;
  due: number;
  restaurantName?: string;
  restaurantAddress?: string;
  restaurantPhone?: string;
}

export const printReceipt = (receiptData: any) => {
  // Create receipt HTML
  const receiptHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Receipt - ${receiptData.orderNo}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          margin: 0;
          padding: 10px;
          width: 300px;
        }
        .receipt {
          border: 1px dashed #000;
          padding: 10px;
          background: white;
        }
        .center { text-align: center; }
        .bold { font-weight: bold; }
        .line { border-top: 1px dashed #000; margin: 10px 0; }
        .row { display: flex; justify-content: space-between; margin: 2px 0; }
        .header { font-size: 14px; margin-bottom: 10px; }
        .items { margin: 10px 0; }
        .totals { margin: 10px 0; }
        .footer { margin-top: 10px; }
        @media print {
          body { margin: 0; }
          .receipt { border: none; }
        }
      </style>
    </head>
    <body>
      <div class="receipt">
        <!-- Header -->
        <div class="center header">
          <div class="bold">${receiptData.restaurantName || 'Restaurant Name'}</div>
          <div>${receiptData.restaurantAddress || '123 Main Street, City'}</div>
          <div>Tel: ${receiptData.restaurantPhone || '(*************'}</div>
        </div>

        <div class="line"></div>

        <!-- Order Info -->
        <div>
          <div>Order No: ${receiptData.orderNo}</div>
          <div style="margin: 5px 0;"></div>
          ${receiptData.table ? `<div>Table: ${receiptData.table}</div>` : ''}
          ${receiptData.table ? `<div style="margin: 5px 0;"></div>` : ''}
          <div>Customer: ${receiptData.customer}</div>
          <div style="margin: 5px 0;"></div>
          <div>Date: ${receiptData.date}</div>
        </div>

        <div class="line"></div>

        <!-- Items Header -->
        <div class="row bold">
          <span>Item</span>
          <span>Total</span>
        </div>

        <!-- Items -->
        <div class="items">
          ${receiptData.items.map(item => `
            <div class="row">
              <span>${item.name} x${item.quantity}</span>
              <span>$${item.total.toFixed(2)}</span>
            </div>
          `).join('')}
        </div>

        <div class="line"></div>

        <!-- Totals -->
        <div class="totals">
          <div class="row">
            <span>Subtotal:</span>
            <span>$${receiptData.subtotal.toFixed(2)}</span>
          </div>
          <div class="row">
            <span>Tax:</span>
            <span>$${receiptData.tax.toFixed(2)}</span>
          </div>
          <div class="row">
            <span>Surcharge:</span>
            <span>$${receiptData.surcharge.toFixed(2)}</span>
          </div>
          <div class="line"></div>
          <div class="row bold">
            <span>Total:</span>
            <span>$${receiptData.total.toFixed(2)}</span>
          </div>
          <div class="row">
            <span>Payment:</span>
            <span>${receiptData.payment}</span>
          </div>
          <div style="margin: 2px 0;">
            <span>//</span>
          </div>
          <div class="row">
            <span>Received:</span>
            <span>$${receiptData.received.toFixed(2)}</span>
          </div>
          <div class="row">
            <span>Received:</span>
            <span>$${receiptData.total.toFixed(2)}</span>
          </div>
          <div class="row">
            <span>Due:</span>
            <span>$${receiptData.due.toFixed(2)}</span>
          </div>
        </div>

        <div class="line"></div>

        <!-- Footer -->
        <div class="center footer">
          <div>Thank you for dining with us!</div>
        </div>
      </div>
    </body>
    </html>
  `;

  // Open print window
  const printWindow = window.open('', '_blank', 'width=400,height=600');
  if (printWindow) {
    printWindow.document.write(receiptHTML);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      // Close window after printing (optional)
      printWindow.onafterprint = () => {
        printWindow.close();
      };
    };
  } else {
    console.error('Could not open print window. Please check popup blocker settings.');
  }
};

// Alternative function for direct browser print
export const printReceiptDirect = (receiptData: any) => {
  const originalContent = document.body.innerHTML;

  const receiptHTML = `
    <div style="font-family: 'Courier New', monospace; font-size: 12px; width: 300px; margin: 0 auto; padding: 10px; border: 1px dashed #000; background: white;">
      <!-- Header -->
      <div style="text-align: center; margin-bottom: 10px;">
        <div style="font-weight: bold; font-size: 14px;">${receiptData.restaurantName || 'Restaurant Name'}</div>
        <div>${receiptData.restaurantAddress || '123 Main Street, City'}</div>
        <div>Tel: ${receiptData.restaurantPhone || '(*************'}</div>
      </div>

      <div style="border-top: 1px dashed #000; margin: 10px 0;"></div>

      <!-- Order Info -->
      <div style="margin-bottom: 10px;">
        <div>Order No: ${receiptData.orderNo}</div>
        <div style="margin: 5px 0;"></div>
        ${receiptData.table ? `<div>Table: ${receiptData.table}</div><div style="margin: 5px 0;"></div>` : ''}
        <div>Customer: ${receiptData.customer}</div>
        <div style="margin: 5px 0;"></div>
        <div>Date: ${receiptData.date}</div>
      </div>

      <div style="border-top: 1px dashed #000; margin: 10px 0;"></div>

      <!-- Items Header -->
      <div style="display: flex; justify-content: space-between; font-weight: bold;">
        <span>Item</span>
        <span>Total</span>
      </div>

      <!-- Items -->
      ${receiptData.items.map(item => `
        <div style="margin: 5px 0;">
          <div style="display: flex; justify-content: space-between;">
            <span>${item.name} x${item.quantity}</span>
            <span>$${item.total.toFixed(2)}</span>
          </div>
        </div>
      `).join('')}

      <div style="border-top: 1px dashed #000; margin: 10px 0;"></div>

      <!-- Totals -->
      <div style="margin-bottom: 10px;">
        <div style="display: flex; justify-content: space-between;">
          <span>Subtotal:</span>
          <span>$${receiptData.subtotal.toFixed(2)}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Tax:</span>
          <span>$${receiptData.tax.toFixed(2)}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Surcharge:</span>
          <span>$${receiptData.surcharge.toFixed(2)}</span>
        </div>
        <div style="border-top: 1px dashed #000; margin: 5px 0;"></div>
        <div style="display: flex; justify-content: space-between; font-weight: bold;">
          <span>Total:</span>
          <span>$${receiptData.total.toFixed(2)}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Payment:</span>
          <span>${receiptData.payment}</span>
        </div>
        <div style="margin: 2px 0;">
          <span>//</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Received:</span>
          <span>$${receiptData.received.toFixed(2)}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Received:</span>
          <span>$${receiptData.total.toFixed(2)}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span>Due:</span>
          <span>$${receiptData.due.toFixed(2)}</span>
        </div>
      </div>

      <div style="border-top: 1px dashed #000; margin: 10px 0;"></div>

      <!-- Footer -->
      <div style="text-align: center; margin-top: 10px;">
        <div>Thank you for dining with us!</div>
      </div>
    </div>
  `;

  document.body.innerHTML = receiptHTML;
  window.print();
  document.body.innerHTML = originalContent;
  window.location.reload(); // Reload to restore the original page
};
