import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import InvoiceItem from "./InvoiceItem";
import { BsArrowRepeat } from "react-icons/bs";
import { LiaSpinnerSolid } from "react-icons/lia";
import Categories from "./Categories";
import LoadCustomerModal from "./table/LoadCutomerModal";
import PaymentModal from './posModals/reportModals/paynowmodal';
import HoldOrdersModal from './posModals/reportModals/HoldOrdersModal';

import GiftCardModal from './posModals/reportModals/GiftCardModal';
import DiscountModal from './posModals/reportModals/DiscountModal';
import ManagerDiscountModal from './posModals/reportModals/ManagerDiscountModal';
import ItemDiscountModal from './posModals/reportModals/ItemDiscountModal';
import ReasonModal from './posModals/ReasonModal';
import {

  selectCartItems,
  selectCartTotal,
  setDiscountAmount as setCartDiscountAmount,
  setDiscountReason as setCartDiscountReason,
  setDiscountType as setCartDiscountType,
  setTaxPercentage as setCartTaxPercentage,
  setLoyaltyPercentage as setCartLoyaltyPercentage,
  setLoyaltyFixedAmount as setCartLoyaltyFixedAmount,
  setLoyaltyType as setCartLoyaltyType,
  selectFinalTotal,
  selectInvoiceNumber,
  selectTipAmount,
  selectLoyaltyAmount,
  selectLoyaltyPercentage,
  generateNewInvoiceNumber,
  setLoyaltyType,
  selectLoyaltyType,
  selectDiscountAmount,
  selectDiscountReason,
  selectDiscountType,
  selectAllForRefund,
  toggleSelectAllForRefund,
  selectIsRefundMode,
  exitRefundMode,
  selectTaxPercentage,

  setSelectedCoupon,
  setCouponOfferAmount,
  clearCoupon,
  clearCart
} from "../store/slices/cartSlice";
import { useGetTaxesQuery } from "../store/api/pos/orderapi";
import { useNavigate } from "react-router-dom";
import { clearSelectedCustomer, selectSelectedCustomer } from "../store/slices/selectedcustomer";

const InvoicePanel: React.FC = () => {
  const dispatch = useDispatch();
  const items = useSelector(selectCartItems);
  const cartTotal = useSelector(selectCartTotal);
  const navigate = useNavigate()
  const finalTotal = useSelector(selectFinalTotal);
  const invoiceNumber = useSelector(selectInvoiceNumber);
  const tipAmount = useSelector(selectTipAmount);
  const loyaltyAmount = useSelector(selectLoyaltyAmount);
  const loyaltyPercentage = useSelector(selectLoyaltyPercentage);
  const loyaltyType = useSelector(selectLoyaltyType)

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isHoldOrdersModalOpen, setIsHoldOrdersModalOpen] = useState(false);

  const [isGiftCardModalOpen, setIsGiftCardModalOpen] = useState(false);
  const [isDiscountDropdownOpen, setIsDiscountDropdownOpen] = useState(false);
  const [isDiscountModalOpen, setIsDiscountModalOpen] = useState(false);
  const [isManagerDiscountModalOpen, setIsManagerDiscountModalOpen] = useState(false);
  const [isReceiptDiscountModalOpen, setIsReceiptDiscountModalOpen] = useState(false);
  const [isItemDiscountModalOpen, setIsItemDiscountModalOpen] = useState(false);
  const [isReasonModalOpen, setIsReasonModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [_selectedDiscount, setSelectedDiscount] = useState<string | null>(null);

  // Local state for UI display - removed as we're using Redux state
  useEffect(() => {
    dispatch(generateNewInvoiceNumber())
  }, [])

  const userId = localStorage.getItem("userId") || ""
  const { data: tax } = useGetTaxesQuery(userId)
  console.log("the tax is ", tax)

  // Calculate total tax percentage from API data
  const calculateTotalTaxPercentage = () => {
    if (!tax || !Array.isArray(tax)) return 0;

    return tax.reduce((total, taxItem) => {
      return total + (parseFloat(taxItem.taxValue) || 0);
    }, 0);
  };

  const totalTaxPercentage = calculateTotalTaxPercentage();

  // Get selectors first
  const selectedCustomer = useSelector(selectSelectedCustomer);
  const currentDiscountAmount = useSelector(selectDiscountAmount);
  const currentDiscountReason = useSelector(selectDiscountReason);
  const currentDiscountType = useSelector(selectDiscountType);
  const selectAllRefund = useSelector(selectAllForRefund);
  const isRefundMode = useSelector(selectIsRefundMode);
  const currentTaxPercentage = useSelector(selectTaxPercentage);

  // Update Redux state when tax data changes (only when not in refund mode)
  useEffect(() => {
    if (totalTaxPercentage > 0 && !isRefundMode) {
      dispatch(setCartTaxPercentage(totalTaxPercentage));
    }
  }, [totalTaxPercentage, dispatch, isRefundMode]);

  const [_selectedGiftCard, setSelectedGiftCard] = useState<{
    cardNumber: string;
    customerName: string;
    balance: number;
  } | null>(null);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleAddNewCustomer = () => {
    console.log("Add new customer logic here");
    closeModal();
  };



  const openGiftCardModal = () => {
    setIsGiftCardModalOpen(true);
  };

  const closeGiftCardModal = () => {
    setIsGiftCardModalOpen(false);
  };

  const handleGiftCardApply = (cardNumber: string, customerName: string, balance: number) => {
    setSelectedGiftCard({ cardNumber, customerName, balance });
    console.log(`Gift card applied: ${cardNumber}, ${customerName}, $${balance}`);
  };

  const discountDropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (discountDropdownRef.current && !discountDropdownRef.current.contains(event.target as Node)) {
        setIsDiscountDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDiscountDropdown = () => {
    setIsDiscountDropdownOpen(prev => !prev);
  };

  const handleDiscountSelect = (discountType: string) => {
    setSelectedDiscount(discountType);
    setIsDiscountDropdownOpen(false);
    console.log(`Discount selected: ${discountType}`);

    if (discountType === "Add Discount") {
      setIsDiscountModalOpen(true);
    } else if (discountType === "Manager Discount") {
      setIsManagerDiscountModalOpen(true);
    } else if (discountType === "Receipt Discount") {
      setIsReceiptDiscountModalOpen(true);
    } else if (discountType === "Item Discount") {
      setIsItemDiscountModalOpen(true);
    }
  };

  // const openDiscountModal = () => {
  //   setIsDiscountModalOpen(true);
  // };

  const closeDiscountModal = () => {
    setIsDiscountModalOpen(false);
  };

  // const openManagerDiscountModal = () => {
  //   setIsManagerDiscountModalOpen(true);
  // };

  const closeManagerDiscountModal = () => {
    setIsManagerDiscountModalOpen(false);
  };

  // const openReceiptDiscountModal = () => {
  //   setIsReceiptDiscountModalOpen(true);
  // };

  const closeReceiptDiscountModal = () => {
    setIsReceiptDiscountModalOpen(false);
  };

  const closeItemDiscountModal = () => {
    setIsItemDiscountModalOpen(false);
  };

  const closeReasonModal = () => {
    setIsReasonModalOpen(false);
  };

  const handleSaveReason = (reason: string) => {
    console.log(`Refund reason saved: ${reason}`);
    // Here you can add logic to save the reason to Redux or send to API
  };

  const handleApplyDiscount = (amount: string, selectedDiscount: any = null, loyaltyData?: { amount: string, percentage?: number, type: string }) => {
    // Only set discount amount, don't touch loyalty
    dispatch(setCartDiscountAmount(amount));

    // Store coupon information if a coupon was selected
    if (selectedDiscount) {
      dispatch(setSelectedCoupon({
        _id: selectedDiscount.id,
        series: selectedDiscount.series,
        description: selectedDiscount.description,
        discount: selectedDiscount.discount,
        discountType: selectedDiscount.discountType
      }));

      // Calculate coupon offer amount based on discount type
      let couponOfferAmount = 0;
      if (selectedDiscount.discountType === '%') {
        // For percentage discounts, calculate based on cart total
        const cartTotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);
        couponOfferAmount = (cartTotal * selectedDiscount.discount) / 100;
      } else {
        // For fixed amount discounts
        couponOfferAmount = selectedDiscount.discount;
      }
      dispatch(setCouponOfferAmount(couponOfferAmount));
    } else {
      // Clear coupon if no discount selected
      dispatch(clearCoupon());
    }

    // Set loyalty data if provided, otherwise reset loyalty to zero
    if (loyaltyData) {
      dispatch(setCartLoyaltyType(loyaltyData.type));

      if (loyaltyData.type === 'percentage' && loyaltyData.percentage !== undefined) {
        dispatch(setCartLoyaltyPercentage(loyaltyData.percentage));
        dispatch(setLoyaltyType('percentage'))
      } else if (loyaltyData.type === 'fixed') {
        dispatch(setCartLoyaltyFixedAmount(loyaltyData.amount));
        dispatch(setLoyaltyType('fixed'))
      }
    } else {
      // Reset loyalty to zero when no loyalty data is provided
      dispatch(setCartLoyaltyType(''));
      dispatch(setCartLoyaltyPercentage(0));
      dispatch(setCartLoyaltyFixedAmount('0'));
    }

    console.log(`Discount amount applied: ${amount}`);
    if (selectedDiscount) {
      console.log(`Coupon applied:`, selectedDiscount);
    }
    if (loyaltyData) {
      console.log(`Loyalty applied:`, loyaltyData);
    }
  };

  const handleApplyManagerDiscount = (amount: string, reason: string, discountType: string) => {
    // Use the renamed action creators from cartSlice
    dispatch(setCartDiscountAmount(amount));
    dispatch(setCartDiscountReason(reason));
    dispatch(setCartDiscountType(discountType));
    console.log(`Manager discount applied: ${amount}, Reason: ${reason}, Type: ${discountType}`);
  };

  const handleApplyReceiptDiscount = (amount: string, reason: string, discountType: string) => {
    // Use the renamed action creators from cartSlice
    dispatch(setCartDiscountAmount(amount));
    dispatch(setCartDiscountReason(reason));
    dispatch(setCartDiscountType(discountType));
    console.log(`Receipt discount applied: ${amount}, Reason: ${reason}, Type: ${discountType}`);
  };




  function handleResetCustomer(_event: React.MouseEvent<SVGElement, MouseEvent>): void {
    throw new Error("Function not implemented.");
  }

  return (
    <div className="bg-white pt-3  rounded-xl flex flex-col overflow-y-auto  h-[88vh] ">
      <div className="px-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold mb-1">Invoice</h2>
          <p className="font-bold">{invoiceNumber}</p>
        </div>

        <div className="flex items-center justify-between mb-2 border-b border-[#E4E4E4] pb-2">
          <h2 className="font-medium text-gray-600">Customer</h2>
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold">
              {selectedCustomer
                ? `${selectedCustomer?.FirstName} ${selectedCustomer?.LastName}`
                : "Guest"}
            </h3>
            <LiaSpinnerSolid
              className="text-gray-500 cursor-pointer hover:text-gray-700"
              size={28}
              onClick={openModal}
              title="Load Customer"
            />
            <BsArrowRepeat
              className="text-orange-500 cursor-pointer hover:text-orange-600"
              size={20}
              onClick={() => {
                dispatch(clearSelectedCustomer());
                if (isRefundMode) {
                  dispatch(exitRefundMode());
                }
              }}
              title={isRefundMode ? "Exit Refund Mode" : "Reset Cart"}
            />

          </div>
        </div>

        <div className="mb-2">
          <Categories
            categories={["Discount", "Gift Card", "Table"]}
            selected={selectedCategory}
            onSelect={(category) => {
              setSelectedCategory(category);
              if (category === "Gift Card") {
                console.log("Gift card selected");
                openGiftCardModal();
              } else if (category === "Discount") {
                console.log("Discount selected");
                toggleDiscountDropdown();
              } else if (category === "Table") {
                navigate("/tables")
              }
            }}
          />

          {/* Discount Dropdown */}
          {isDiscountDropdownOpen && (
            <div
              ref={discountDropdownRef}
              className="absolute left-5 top-36 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50"
            >
              <div className="py-1">
                <button
                  onClick={() => handleDiscountSelect("Add Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Add Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Item Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Item Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Manager Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500 border-b border-gray-100"
                >
                  Manager Discount
                </button>
                <button
                  onClick={() => handleDiscountSelect("Receipt Discount")}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-500"
                >
                  Receipt Discount
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Select All for Refund - Only show in refund mode */}
      {isRefundMode && (
        <div className="px-4 py-2">
          <label className="flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={selectAllRefund}
              onChange={() => dispatch(toggleSelectAllForRefund())}
              className="mr-2 w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
            />
            <span className="text-sm font-medium text-gray-700">Select All for Refund</span>
          </label>
        </div>
      )}

      {/* Scrollable Items */}
      <div className="flex-1 overflow-y-auto px-4 space-y-2 ">
        {items.map((item: any) => (
          <InvoiceItem
            key={item.id}
            id={item.id}
            name={item.name}
            price={item.price}
            image={item.image}
            quantity={item.quantity}
            modifiers={item.modifiers}
            hasModifiers={item.hasModifiers}
            note={item.note}
            discountType={item.discountType}
            discountAmount={item.discountAmount}
            originalPrice={item.originalPrice}
            selectedForRefund={item.selectedForRefund}
            isRefundMode={isRefundMode}
          />
        ))}
      </div>

      {/* Sticky Bottom Panel */}
      <div className="px-4 py-1 border-t border-[#E4E4E4] bg-white mt-auto">
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Item Total</span>
          <span className="text-black font-bold">${cartTotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">
            {currentDiscountReason ? `Discount (${currentDiscountReason})` : 'Discount'}
            {currentDiscountType === 'percentage' && currentDiscountAmount ? ` ${currentDiscountAmount}%` : ''}
          </span>
          <span className="text-black font-bold">
            ${currentDiscountType === 'percentage'
              ? ((cartTotal * (Number(currentDiscountAmount) || 0)) / 100).toFixed(2)
              : (Number(currentDiscountAmount) || 0).toFixed(2)
            }
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-gray-600">
            Loyalty {loyaltyType === 'percentage' && loyaltyPercentage > 0 ? `(${loyaltyPercentage}%)` : ''}
          </span>
          <span className="text-black font-bold">
            ${loyaltyAmount ? loyaltyAmount.toFixed(2) : "0.00"}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Tip</span>
          <span className="text-black font-bold">
            ${tipAmount ? (Number(tipAmount) || 0).toFixed(2) : "0.00"}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-2">
          <span className="text-natural">Tax ({currentTaxPercentage}%)</span>
          <span className="text-black font-bold">
            ${(cartTotal * (currentTaxPercentage / 100)).toFixed(2)}
          </span>
        </div>
        <div className="flex justify-between text-[16px] mb-4 border-t border-gray-200 pt-2">
          <span className="text-natural font-bold">Total</span>
          <span className="text-orange font-bold text-xl">
            ${finalTotal.toFixed(2)}
          </span>
        </div>

        <div className="flex gap-2">
          {isRefundMode ? (
            <>
              <button
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer"
                onClick={() => dispatch(clearCart())}
              >
                Refund Item
              </button>
              <button
                onClick={() => setIsReasonModalOpen(true)}
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer transition-colors"
              >
                Reason
              </button>
              <button onClick={() => setIsPaymentModalOpen(true)} className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer">
                Pay Now
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setIsHoldOrdersModalOpen(true)}
                className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer"
              >
                Hold
              </button>
              <button className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer transition-colors">
                Split
              </button>
              <button onClick={() => setIsPaymentModalOpen(true)} className="flex-1 bg-orange text-white font-bold text-sm py-2 rounded-full cursor-pointer">
                Pay Now
              </button>
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <LoadCustomerModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onAddNewCustomer={handleAddNewCustomer}
        onSelectCustomer={handleResetCustomer}
      />

      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        totalAmount={finalTotal}
      />

      <HoldOrdersModal
        isOpen={isHoldOrdersModalOpen}
        onClose={() => setIsHoldOrdersModalOpen(false)}
      />

      <GiftCardModal
        isOpen={isGiftCardModalOpen}
        onClose={closeGiftCardModal}
        onApply={handleGiftCardApply}
      />

      <DiscountModal
        isOpen={isDiscountModalOpen}
        onClose={closeDiscountModal}
        onApply={handleApplyDiscount}
      />

      <ManagerDiscountModal
        isOpen={isManagerDiscountModalOpen}
        onClose={closeManagerDiscountModal}
        onApply={handleApplyManagerDiscount}
        title="Manager Discount"
        initialAmount={currentDiscountAmount}
        initialReason={currentDiscountReason}
        initialDiscountType={currentDiscountType}
      />

      <ManagerDiscountModal
        isOpen={isReceiptDiscountModalOpen}
        onClose={closeReceiptDiscountModal}
        onApply={handleApplyReceiptDiscount}
        title="Receipt Discount"
        initialAmount={currentDiscountAmount}
        initialReason={currentDiscountReason}
        initialDiscountType={currentDiscountType}
      />

      <ItemDiscountModal
        isOpen={isItemDiscountModalOpen}
        onClose={closeItemDiscountModal}
        items={items}
      />

      <ReasonModal
        isOpen={isReasonModalOpen}
        onClose={closeReasonModal}
        onSave={handleSaveReason}
      />
    </div >
  );
};

export default InvoicePanel;